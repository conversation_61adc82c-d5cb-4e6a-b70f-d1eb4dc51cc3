# Schema Inference Approach - Two-Pass System Task Breakdown

## Project Overview

**Goal**: Implement a two-pass schema inference system to solve DuckDB's JSON extension memory pressure problems (25GB+ files causing OOM errors).

**Approach**: 
- Pass 1: Stream through JSON file to infer complete schema and calculate exact vector capacities
- Pass 2: Stream data directly into pre-allocated DuckDB vectors with known offsets
- Expected: O(schema_complexity + vector_batch_size) memory vs current O(file_size)

## Phase 1: Schema Inference Engine - Foundation

### Task 1.1: Design Schema Inference Architecture

**Objective**: Design the core architecture for streaming schema discovery

**Context References**:
- `design-elements/design_decisions.md` - Review recursive helper function patterns and anti-patterns
- `design-elements/duckdb_memory_layout_diagrams.md` - Study vector capacity requirements
- Current `JsonValueType` enum in `src/lib.rs` - Understand existing type system

**Key Design Decisions**:
1. How to represent inferred schemas in memory
2. Memory bounds for schema complexity (target: <10MB for any schema)
3. Handling of heterogeneous JSON objects (schema merging strategy)
4. Recursive schema representation without hardcoded depth limits

**Deliverables**:
- Schema representation data structures
- Memory usage analysis and bounds
- Architecture documentation

### Task 1.2: Implement Streaming Schema Discovery

**Objective**: Build schema inference engine using struson for memory-efficient JSON parsing

**Context References**:
- Current struson usage patterns in `src/lib.rs` around `JsonStreamReader`
- `design-elements/duckdb_nested_types_reference.md` - Proper type mapping to DuckDB types
- `design-elements/design_decisions.md` - Anti-patterns: no hardcoded nesting limits

**Critical Requirements**:
- Must handle arbitrary nesting depth without hardcoded limits
- Use pure struson streaming
- Track schema complexity to verify memory bounds
- Handle all JSON value types (null, boolean, number, string, array, object)

**Implementation Strategy**:
```rust
struct SchemaInferrer {
    current_schema: JsonSchema,
    nesting_stack: Vec<SchemaContext>,
    memory_tracker: MemoryUsageTracker,
}
```

**Deliverables**:
- Streaming schema inference implementation
- Memory usage validation
- Test suite for various JSON structures

### Task 1.3: Build Vector Capacity Calculator

**Objective**: Implement precise capacity calculation for DuckDB vectors based on inferred schema

**Context References**:
- `design-elements/duckdb_memory_layout_diagrams.md` - Cumulative offset patterns
- Existing capacity management in `src/lib.rs` around `list_vector.child()` calls
- Failed offset approaches in current codebase (avoid "estimated offset" anti-patterns)

**Critical Requirements**:
- Calculate exact capacities to avoid memory waste or reallocation
- Handle multi-dimensional arrays with proper capacity calculation
- Support heterogeneous object schemas
- Provide capacity breakdown for debugging

**Implementation Strategy**:
```rust
struct VectorCapacityCalculator {
    schema: JsonSchema,
    row_count: usize,
}

impl VectorCapacityCalculator {
    fn calculate_list_capacity(&self, array_schema: &ArraySchema) -> usize;
    fn calculate_struct_capacity(&self, object_schema: &ObjectSchema) -> usize;
    fn calculate_total_memory_requirement(&self) -> MemoryRequirement;
}
```

**Deliverables**:
- Capacity calculation algorithms
- Memory requirement estimation
- Validation against actual vector usage

### Task 1.4: Create Schema Representation System

**Objective**: Design and implement internal schema representation that captures JSON structure for efficient vector allocation

**Context References**:
- Existing `JsonValueType` enum in `src/lib.rs`
- Existing `JsonSchema` struct in `src/lib.rs`
- `design-elements/duckdb_nested_types_reference.md` - DuckDB type system mapping

**Requirements**:
- Support recursive structures (arrays of arrays, nested objects)
- Handle schema merging for heterogeneous objects
- Provide memory-efficient storage
- Enable efficient capacity calculation

**Implementation Strategy**:
```rust
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub statistics: SchemaStatistics,
}

#[derive(Debug, Clone)]
pub enum InferredJsonType {
    Null,
    Boolean,
    Number,
    String { max_length: Option<usize> },
    Array { 
        element_type: Box<InferredJsonType>,
        max_length: Option<usize>,
        total_elements: usize,
    },
    Object { 
        fields: HashMap<String, InferredJsonType>,
        is_homogeneous: bool,
    },
}

#[derive(Debug, Clone)]
pub struct SchemaStatistics {
    pub total_rows: usize,
    pub max_nesting_depth: usize,
    pub memory_complexity: usize,
}
```

**Deliverables**:
- Schema representation types
- Schema merging algorithms
- Memory complexity calculation
- Serialization/deserialization for debugging

## Phase 2: Two-Pass File Processing

### Task 2.1: Implement Pass 1 - Schema Inference Pass

**Objective**: Build the first pass that scans entire JSON file to infer complete schema and calculate vector capacities

**Context References**:
- Struson streaming patterns from existing code
- `design-elements/design_decisions.md` - Prohibition of "For Now" implementations
- Memory usage tracking requirements

**Critical Requirements**:
- Must be complete and correct (no approximations)
- Track memory usage to verify O(schema_complexity) bounds
- Handle malformed JSON gracefully
- Provide progress reporting for large files

**Implementation Strategy**:
```rust
pub struct SchemaInferencePass {
    inferrer: SchemaInferrer,
    capacity_calculator: VectorCapacityCalculator,
    progress_tracker: ProgressTracker,
}

impl SchemaInferencePass {
    pub fn infer_schema_from_file(&mut self, file_path: &str) -> Result<InferredSchema, Error>;
    pub fn calculate_vector_capacities(&self, schema: &InferredSchema) -> VectorCapacities;
}
```

**Deliverables**:
- Complete Pass 1 implementation
- Memory usage validation
- Progress reporting system
- Error handling for malformed JSON

### Task 2.2: Implement Pass 2 - Streaming Data Loader

**Objective**: Build second pass that streams JSON data directly into pre-allocated DuckDB vectors using known schema and capacities

**Context References**:
- Existing vector insertion patterns in `src/lib.rs`
- `insert_multidimensional_array_recursive` function (study for patterns, avoid its offset bugs)
- `design-elements/duckdb_memory_layout_diagrams.md` - Proper cumulative offset management

**Critical Requirements**:
- Use proper cumulative offset management (not estimated offsets)
- Pre-allocate all vectors with exact capacities from Pass 1
- Stream data efficiently without intermediate storage
- Handle schema validation (data must match inferred schema)

**Implementation Strategy**:
```rust
pub struct StreamingDataLoader {
    schema: InferredSchema,
    vector_capacities: VectorCapacities,
    offset_tracker: CumulativeOffsetTracker,
    pre_allocated_vectors: PreAllocatedVectors,
}

impl StreamingDataLoader {
    pub fn load_data_from_file(&mut self, file_path: &str, output: &DataChunkHandle) -> Result<(), Error>;
}
```

**Deliverables**:
- Complete Pass 2 implementation
- Proper offset coordination system
- Schema validation during loading
- Performance optimization

### Task 2.3: Implement File Position Management

**Objective**: Build system to efficiently restart JSON parsing from beginning for Pass 2 after Pass 1 completes

**Context References**:
- Research struson's file seeking capabilities
- Consider memory mapping vs file reopening trade-offs
- Must handle large files (25GB+) efficiently

**Requirements**:
- Efficient file reopening/repositioning between passes
- Handle large files without memory mapping entire file
- Maintain file handle state across passes
- Error handling for file system issues

**Implementation Strategy**:
```rust
pub struct FileManager {
    file_path: String,
    file_size: u64,
    current_position: u64,
}

impl FileManager {
    pub fn open_for_schema_inference(&mut self) -> Result<JsonStreamReader<File>, Error>;
    pub fn reopen_for_data_loading(&mut self) -> Result<JsonStreamReader<File>, Error>;
    pub fn get_progress_percentage(&self) -> f64;
}
```

**Deliverables**:
- File management system
- Progress tracking across passes
- Error handling for file operations

### Task 2.4: Build Offset Coordination System

**Objective**: Implement proper cumulative offset tracking across all rows and nested structures for Pass 2

**Context References**:
- **CRITICAL**: `design-elements/duckdb_memory_layout_diagrams.md` for correct offset patterns
- Study failed approaches in current codebase to avoid repeating "estimated offset" anti-patterns
- `design-elements/design_decisions.md` - Prohibition of "For Now" implementations

**Critical Requirements**:
- Exact cumulative offsets (no approximations or estimates)
- Coordinate offsets across all rows and nested structures
- Handle multi-dimensional arrays correctly
- Provide offset debugging and validation

**Implementation Strategy**:
```rust
pub struct CumulativeOffsetTracker {
    current_offsets: HashMap<VectorPath, usize>,
    capacity_limits: HashMap<VectorPath, usize>,
    debug_mode: bool,
}

impl CumulativeOffsetTracker {
    pub fn allocate_range(&mut self, vector_path: &VectorPath, length: usize) -> OffsetRange;
    pub fn validate_capacity_usage(&self) -> Result<(), CapacityError>;
    pub fn get_debug_report(&self) -> OffsetDebugReport;
}
```

**Deliverables**:
- Offset coordination system
- Capacity validation
- Debug reporting for offset tracking
- Integration with vector allocation

## Phase 3: Memory Management & Validation

### Task 3.1: Implement Memory Usage Monitoring

**Objective**: Build memory tracking and reporting to validate O(schema_complexity + vector_batch_size) memory usage vs O(file_size)

**Context References**:
- Must prove the approach solves the 25GB JSON OOM problem identified in research
- Target: 100x-1000x memory reduction vs current DuckDB JSON extension

**Requirements**:
- Real-time memory usage tracking
- Memory profiling hooks for both passes
- Comparison with baseline memory usage
- Memory limit enforcement

**Implementation Strategy**:
```rust
pub struct MemoryMonitor {
    baseline_memory: usize,
    peak_memory: usize,
    current_memory: usize,
    memory_limit: Option<usize>,
}

impl MemoryMonitor {
    pub fn start_monitoring(&mut self);
    pub fn record_checkpoint(&mut self, checkpoint_name: &str);
    pub fn get_memory_report(&self) -> MemoryReport;
    pub fn validate_memory_bounds(&self) -> Result<(), MemoryError>;
}
```

**Deliverables**:
- Memory monitoring system
- Memory usage reports
- Memory limit enforcement
- Performance profiling integration

### Task 3.2: Create Large File Test Suite

**Objective**: Build comprehensive tests for large JSON files with various schemas to validate memory efficiency

**Context References**:
- Reference existing test patterns in `tests/` directory
- Must include multi-dimensional arrays, deeply nested objects, and heterogeneous schemas
- Test files should approach memory limits of target systems

**Requirements**:
- Generate large JSON files with known schemas
- Test various JSON structures (arrays, objects, mixed nesting)
- Validate memory usage stays within bounds
- Performance benchmarking

**Test Categories**:
1. **Large Arrays**: Multi-million element arrays with various nesting depths
2. **Deep Objects**: Deeply nested object structures (100+ levels)
3. **Heterogeneous Data**: Mixed schemas within arrays/objects
4. **Multi-dimensional Arrays**: 4D+ arrays with irregular dimensions
5. **Real-world Patterns**: Common JSON patterns from actual datasets

**Deliverables**:
- Large file test generation
- Memory usage validation tests
- Performance benchmark suite
- Regression test framework

### Task 3.3: Validate Against Current DuckDB JSON Extension

**Objective**: Create comparative benchmarks showing memory usage improvements over DuckDB's default JSON extension

**Context References**:
- Document the specific OOM scenarios that current extension fails on
- Prove 100x-1000x memory reduction claims with concrete measurements

**Requirements**:
- Side-by-side memory usage comparison
- Performance comparison (time vs memory trade-off)
- File size limits comparison
- Feature compatibility validation

**Benchmark Categories**:
1. **Memory Usage**: Peak memory consumption for various file sizes
2. **Processing Time**: Time overhead of two-pass approach
3. **File Size Limits**: Maximum file size before OOM
4. **Schema Complexity**: Performance with various JSON structures

**Deliverables**:
- Comparative benchmark suite
- Memory usage analysis
- Performance trade-off documentation
- Compatibility validation

## Phase 4: Integration & Error Handling

### Task 4.1: Integrate with DuckDB Extension Framework

**Objective**: Modify existing extension entry points to use two-pass system instead of current single-pass approach

**Context References**:
- Study current integration in `src/lib.rs` around `streaming_json_reader_function`
- Must maintain API compatibility while switching to two-pass internally

**Requirements**:
- Maintain existing API surface
- Switch internal implementation to two-pass
- Handle backward compatibility
- Update extension metadata

**Implementation Strategy**:
```rust
// Modify existing function to use two-pass internally
pub fn streaming_json_reader_function(
    info: &FunctionInfo,
    input: &DataChunk,
    output: &mut DataChunk,
) -> Result<(), Box<dyn std::error::Error>> {
    // Phase 1: Schema inference
    let schema = infer_schema_from_file(&file_path)?;

    // Phase 2: Data loading with pre-allocated vectors
    let loader = StreamingDataLoader::new(schema);
    loader.load_data_to_vectors(output)?;

    Ok(())
}
```

**Deliverables**:
- Updated extension entry points
- API compatibility validation
- Integration testing
- Documentation updates

### Task 4.2: Implement Comprehensive Error Handling

**Objective**: Build robust error handling for both passes, including file I/O errors, malformed JSON, schema conflicts, and memory allocation failures

**Context References**:
- Review existing error patterns in codebase
- Must handle partial failures gracefully and provide clear error messages

**Error Categories**:
1. **File I/O Errors**: File not found, permission denied, disk full
2. **JSON Parsing Errors**: Malformed JSON, unexpected structure
3. **Schema Errors**: Schema inference failures, schema conflicts
4. **Memory Errors**: Allocation failures, capacity exceeded
5. **DuckDB Integration Errors**: Vector allocation failures, type mismatches

**Implementation Strategy**:
```rust
#[derive(Debug, thiserror::Error)]
pub enum SchemaInferenceError {
    #[error("File I/O error: {0}")]
    FileError(#[from] std::io::Error),

    #[error("JSON parsing error at position {position}: {message}")]
    JsonError { position: usize, message: String },

    #[error("Schema inference failed: {0}")]
    SchemaError(String),

    #[error("Memory allocation failed: {0}")]
    MemoryError(String),

    #[error("DuckDB integration error: {0}")]
    DuckDBError(String),
}
```

**Deliverables**:
- Comprehensive error handling system
- Clear error messages and recovery strategies
- Error logging and debugging support
- User-friendly error reporting

### Task 4.3: Add Configuration Options

**Objective**: Implement user-configurable options for memory limits, batch sizes, and schema inference behavior

**Context References**:
- Study existing parameter handling in DuckDB extension
- Allow users to tune memory vs performance trade-offs

**Configuration Options**:
1. **Memory Limits**: Maximum memory usage for schema inference and data loading
2. **Batch Sizes**: Vector batch sizes for memory management
3. **Schema Options**: Schema inference behavior, heterogeneous object handling
4. **Performance Options**: Progress reporting, debug output, profiling

**Implementation Strategy**:
```rust
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub max_memory_usage: Option<usize>,
    pub vector_batch_size: usize,
    pub schema_inference_mode: SchemaInferenceMode,
    pub enable_progress_reporting: bool,
    pub enable_debug_output: bool,
}
```

**Deliverables**:
- Configuration system
- Parameter validation
- Default configuration optimization
- Configuration documentation

### Task 4.4: Update Documentation and Design Decisions

**Objective**: Document the two-pass approach, its trade-offs, and architectural lessons learned

**Context References**:
- Update `design-elements/design_decisions.md` with new architectural patterns
- Document memory usage characteristics and performance trade-offs
- Include guidance on when to use this approach vs alternatives

**Documentation Requirements**:
1. **Architecture Documentation**: Two-pass system design and implementation
2. **Performance Characteristics**: Memory usage, time complexity, trade-offs
3. **Usage Guidelines**: When to use, configuration recommendations
4. **Troubleshooting**: Common issues and solutions
5. **Design Lessons**: Architectural patterns and anti-patterns learned

**Deliverables**:
- Updated design decisions documentation
- User guide for two-pass system
- Performance characteristics documentation
- Troubleshooting guide
- Architectural lessons documentation

## Success Criteria

### Memory Efficiency Goals
- **Target**: 100x-1000x memory reduction vs current DuckDB JSON extension
- **Specific**: Process 25GB JSON files with <100MB peak memory usage
- **Validation**: Comprehensive benchmarks proving memory bounds

### Performance Goals
- **Acceptable**: 2x time overhead for 100x+ memory improvement
- **Target**: Sub-linear memory growth with file size
- **Validation**: Performance benchmarks across various file sizes and schemas

### Compatibility Goals
- **API Compatibility**: Maintain existing extension API
- **Feature Compatibility**: Support all current JSON processing features
- **Schema Support**: Handle arbitrary JSON structures without limitations

### Quality Goals
- **Reliability**: Robust error handling and recovery
- **Maintainability**: Clean, well-documented code following established patterns
- **Testability**: Comprehensive test suite with large file validation
